import asyncio
import logging
from typing import TypedDict
from uuid import UUID

from fastapi import UploadFile
from openai.types.chat import ChatCompletionMessageParam

from constants.engagement import (
    ENGAGEMENT_SUMMARY_MAX_TOKENS,
    ONE_LINE_DESCRIPTION_MAX_TOKENS,
    EngagementMessageIntention,
)
from constants.extracted_data import Tense
from constants.message import MessageRole, MessageType, PageType, QualFieldName, SystemReplyType, TextEditCommand
from constants.prompt import SummaryPrompts, prompt_templates
from repositories import ConversationMessageRepository, ConversationRepository, FieldRepository, OpenAIRepository
from schemas import (
    BaseMessageSerializer,
    CombinedMessageSerializer,
    Command,
    EngagementMessageIntentClassifierServiceResponse,
    MessageValidator,
    Option,
    QualFields,
    SumaryResponse,
    SystemMessageSerializer,
    TextField,
    UserMessageSerializer,
)
from services.command import CommandService
from services.engagement_field_modification import Engagement<PERSON>ieldModificationService
from services.engagement_intent_processor import EngagementIntentProcessor
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.message_handlers.base import MessageHandler


__all__ = ['EngagementDescriptionMessageHandler']

logger = logging.getLogger(__name__)


class _SystemMessageData(TypedDict):
    content: str
    message_type: MessageType
    is_error: bool
    system_reply_type: SystemReplyType | None
    qual_fields: QualFields | None
    is_undoable: bool


class EngagementDescriptionMessageHandler(MessageHandler):
    FIELD_MODIFICATION_INTENTS = {
        EngagementMessageIntention.BUSINESS_ISSUES,
        EngagementMessageIntention.SCOPE_APPROACH,
        EngagementMessageIntention.VALUE_DELIVERED_IMPACT,
        EngagementMessageIntention.ENGAGEMENT_SUMMARY,
        EngagementMessageIntention.ONE_LINE_DESCRIPTION,
    }
    TEXT_EDIT_COMMANDS = {
        TextEditCommand.EXPAND,
        TextEditCommand.SHORTEN,
        TextEditCommand.REWRITE,
        TextEditCommand.PROMPT,
    }
    REGENERATION_TARGET_INTENTS = {
        EngagementMessageIntention.BUSINESS_ISSUES,
        EngagementMessageIntention.SCOPE_APPROACH,
        EngagementMessageIntention.VALUE_DELIVERED_IMPACT,
    }
    REGENERATE_MAP = {
        QualFieldName.ENGAGEMENT_SUMMARY: {
            'system_prompt': prompt_templates.extract_engagement_summary,
            'user_prompt': prompt_templates.extract_engagement_summary,
            'max_tokens': ENGAGEMENT_SUMMARY_MAX_TOKENS,
            'response_format': SumaryResponse,
        },
        QualFieldName.ONE_LINE_DESCRIPTION: {
            'system_prompt': prompt_templates.extract_one_line_summary,
            'user_prompt': prompt_templates.extract_one_line_summary,
            'max_tokens': ONE_LINE_DESCRIPTION_MAX_TOKENS,
            'response_format': SumaryResponse,
        },
    }

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        openai_repository: OpenAIRepository,
        engagement_intent_processor: EngagementIntentProcessor,
        engagement_field_modification_service: EngagementFieldModificationService,
        command_service: CommandService,
        field_repository: FieldRepository,
        conversation_repository: ConversationRepository,
        extracted_data_service: ExtractedDataService,
    ):
        # Core repositories and services
        self.conversation_message_repository = conversation_message_repository
        self.openai_repository = openai_repository
        self.engagement_intent_processor = engagement_intent_processor
        self.engagement_field_modification_service = engagement_field_modification_service
        self.command_service = command_service
        self.field_repository = field_repository
        self.conversation_repository = conversation_repository
        self.extracted_data_service = extracted_data_service

        # Intent classifier setup
        self.engagement_intent_classifier_service = IntentClassifierService(
            openai_service=openai_repository,
            system_prompt_template=prompt_templates.engagement_description_page_intention.SYSTEM,
            user_prompt_template=prompt_templates.engagement_description_page_intention.USER,
            intentions=prompt_templates.engagement_description_page_intention.INTENTIONS,
        )

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        page_type = PageType.ENGAGEMENT_DESCRIPTION

        # Handle client name replacement command specially
        if command and command.command == TextEditCommand.REPLACE_CLIENT_NAME_CONFIRM:
            return await self._handle_client_name_replacement_command(
                conversation_id, content, selected_option, command, files, page_type
            )

        # Normal flow for other commands
        user_message = await self._create_user_message(
            conversation_id, content, selected_option, command, files, page_type
        )

        await self._anonymize_all_fields(conversation_id)

        intent = await self._classify_intent(content)

        system_message_data = await self._process_intent(intent, command, content, conversation_id)

        system_message = await self._create_system_message(conversation_id, page_type, system_message_data)

        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message),
            system=SystemMessageSerializer.model_validate(system_message),
        )

    async def _handle_client_name_replacement_command(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,
        files: list[UploadFile] | None,
        page_type: PageType,
    ) -> CombinedMessageSerializer:
        """
        Handle client name replacement command.
        Always creates messages but uses detection logic to determine system message type.
        """
        # Always create user message
        user_message = await self._create_user_message(
            conversation_id, content, selected_option, command, files, page_type
        )

        # Use detection logic to determine system message type
        intent = await self._classify_intent(content)
        system_message_data = await self._process_intent(intent, command, content, conversation_id)

        # Always run anonymization for consistency
        await self._anonymize_all_fields(conversation_id)
        # Update system message based on detection results
        if system_message_data['system_reply_type'] == SystemReplyType.CLIENT_NAME_REPLACEMENT_NEEDED:
            # Client name found - show replacement confirmation
            system_message_data['system_reply_type'] = SystemReplyType.CLIENT_NAME_REPLACED
            system_message_data['content'] = SystemReplyType.CLIENT_NAME_REPLACED.message_text.format(
                client_name=await self._get_client_name(conversation_id)
            )
        else:
            # No client name found - show empty message
            system_message_data['system_reply_type'] = SystemReplyType.EMPTY
            system_message_data['content'] = ''
            system_message_data['qual_fields'] = None

        system_message = await self._create_system_message(conversation_id, page_type, system_message_data)

        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message),
            system=SystemMessageSerializer.model_validate(system_message),
        )

    async def _get_client_name(self, conversation_id: UUID) -> str:
        """Helper method to get client name from confirmed data."""
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        return confirmed_data.client_name or ''

    async def _create_user_message(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,
        files: list[UploadFile] | None,
        page_type: PageType,
    ) -> BaseMessageSerializer:
        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=content,
            selected_option=selected_option,
            command=command,
            files=files,
            system_reply_type=None,
            page_type=page_type,
        )
        return await self.conversation_message_repository.create(user_message_to_persist)

    async def _classify_intent(self, content: str) -> EngagementMessageIntention:
        intent_classification_response: EngagementMessageIntentClassifierServiceResponse = (
            await self.engagement_intent_classifier_service.classify_intent(
                user_message=content,
                response_cls=EngagementMessageIntentClassifierServiceResponse,
                intention_enum=EngagementMessageIntention,
            )
        )
        return intent_classification_response.intention

    async def _process_intent(
        self,
        intent: EngagementMessageIntention,
        command: Command | None,
        content: str,
        conversation_id: UUID,
    ) -> _SystemMessageData:
        if intent == EngagementMessageIntention.FORMATTING:
            return self._handle_other_intent(intent)
        if command:
            return await self._handle_command_intent(command, content, conversation_id, intent)
        if self._is_field_modification_intent(intent):
            return await self._handle_field_modification_intent(intent, content, conversation_id)

        return self._handle_other_intent(intent)

    async def _handle_command_intent(
        self, command: Command, content: str, conversation_id: UUID, intent: EngagementMessageIntention
    ) -> _SystemMessageData:
        system_message = await self.command_service.process_command(command, content, conversation_id)
        qual_fields = system_message.qual_fields

        if self._should_trigger_conditional_regeneration(command, intent):
            qual_fields = await self._regenerate_summary_fields_conditionally(conversation_id, qual_fields)

        return {
            'content': system_message.content,
            'message_type': system_message.type,
            'is_error': system_message.is_error,
            'system_reply_type': system_message.system_reply_type,
            'qual_fields': qual_fields,
            'is_undoable': system_message.is_undoable,
        }

    async def _handle_field_modification_intent(
        self, intent: EngagementMessageIntention, content: str, conversation_id: UUID
    ) -> _SystemMessageData:
        modification_result = await self.engagement_field_modification_service.modify_field(
            conversation_id=conversation_id,
            intent=intent,
            user_request=content,
        )

        if not modification_result.success:
            error_message = modification_result.error or 'Unknown error occurred'
            return {
                'content': f'❌ Failed to update the field: {error_message}. Please try again or rephrase your request.',
                'message_type': MessageType.ERROR,
                'is_error': True,
                'system_reply_type': SystemReplyType.ENGAGEMENT_DETAILS_FIELD_ERROR,
                'qual_fields': None,
                'is_undoable': True,
            }

        processed_result = self.engagement_intent_processor.process_intent(intent)
        reply_type = self.engagement_intent_processor.process_intent_reply_type(intent)
        qual_fields = None
        if modification_result.field_name and modification_result.updated_content:
            qual_fields = QualFields(
                **{
                    modification_result.field_name: TextField(
                        context=modification_result.updated_content,
                        snippet=modification_result.updated_content,
                    )
                }
            )

        if intent in self.REGENERATION_TARGET_INTENTS:
            qual_fields = await self._regenerate_summary_fields_conditionally(conversation_id, qual_fields)

        return {
            'content': processed_result.response,
            'message_type': MessageType.TEXT,
            'is_error': False,
            'system_reply_type': reply_type,
            'qual_fields': qual_fields,
            'is_undoable': True,
        }

    def _handle_other_intent(self, intent: EngagementMessageIntention) -> _SystemMessageData:
        processed_result = self.engagement_intent_processor.process_intent(intent)
        reply_type = self.engagement_intent_processor.process_intent_reply_type(intent)

        return {
            'content': processed_result.response,
            'message_type': MessageType.TEXT,
            'is_error': False,
            'system_reply_type': reply_type,
            'qual_fields': None,
            'is_undoable': False,
        }

    async def _create_system_message(
        self,
        conversation_id: UUID,
        page_type: PageType,
        system_message_data: _SystemMessageData,
    ) -> BaseMessageSerializer:
        system_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=system_message_data['message_type'],
            content=system_message_data['content'],
            system_reply_type=system_message_data['system_reply_type'],
            is_error=system_message_data['is_error'],
            page_type=page_type,
            qual_fields=system_message_data['qual_fields'],
            is_undoable=system_message_data['is_undoable'],
        )
        return await self.conversation_message_repository.create(system_message_to_persist)

    async def _anonymize_all_fields(self, conversation_id: UUID) -> None:
        """
        Anonymize all relevant fields by replacing the client name.
        """
        try:
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
            client_name = confirmed_data.client_name
            if not client_name:
                return

            fields_to_update = [
                QualFieldName.BUSINESS_ISSUES,
                QualFieldName.SCOPE_APPROACH,
                QualFieldName.VALUE_DELIVERED,
                QualFieldName.ENGAGEMENT_SUMMARY,
                QualFieldName.ONE_LINE_DESCRIPTION,
            ]

            for field_name in fields_to_update:
                latest_field = await self.field_repository.get_latest_value(conversation_id, field_name)
                if latest_field and latest_field.FieldValue is not None:
                    original_text = str(latest_field.FieldValue)
                    updated_text = original_text.replace(client_name, 'the client')
                    if original_text != updated_text:
                        await self.field_repository.create(
                            conversation_id=conversation_id,
                            field_name=field_name,
                            field_value=updated_text,
                            formatted_field_value=updated_text,
                        )
        except Exception as e:
            logger.error(f'Anonymization failed for conversation {conversation_id}: {e}')

    def _is_field_modification_intent(self, intent: EngagementMessageIntention) -> bool:
        """
        Check if the intent is for field modification.

        Args:
            intent: The classified engagement message intention

        Returns:
            True if the intent is for field modification, False otherwise
        """
        return intent in self.FIELD_MODIFICATION_INTENTS

    def _should_trigger_conditional_regeneration(self, command: Command, intent: EngagementMessageIntention) -> bool:
        """
        Check if conditional field regeneration should be triggered.

        Args:
            command: The command being processed
            intent: The classified user intention

        Returns:
            True if conditional regeneration should be triggered, False otherwise
        """
        is_chat_command = command.command in self.TEXT_EDIT_COMMANDS and not command.is_undo_rte
        is_undo_command = command.command == TextEditCommand.UNDO and command.field_name.is_regenerator
        return (is_chat_command and intent in self.REGENERATION_TARGET_INTENTS) or is_undo_command

    async def _regenerate_summary_fields_conditionally(
        self, conversation_id: UUID, original_qual_fields: QualFields | None
    ) -> QualFields | None:
        """
        Regenerate summary fields and merge them with existing qual_fields.

        Args:
            conversation_id: The conversation ID
            original_qual_fields: The original qual_fields from command processing

        Returns:
            The updated QualFields object or original if regeneration fails
        """
        try:
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
            tense = confirmed_data.tense if confirmed_data and confirmed_data.tense else Tense.PRESENT
            context = await self._get_summaries_context_for_regeneration(conversation_id)

            # Check if any of the required fields are empty/None
            business_issues_field = await self.field_repository.get_latest_value(
                conversation_id, QualFieldName.BUSINESS_ISSUES
            )
            scope_approach_field = await self.field_repository.get_latest_value(
                conversation_id, QualFieldName.SCOPE_APPROACH
            )
            value_delivered_field = await self.field_repository.get_latest_value(
                conversation_id, QualFieldName.VALUE_DELIVERED
            )

            # Extract string values from repository (primary source)
            business_issues = (
                str(business_issues_field.FieldValue)
                if business_issues_field and business_issues_field.FieldValue is not None
                else ''
            )
            scope_approach = (
                str(scope_approach_field.FieldValue)
                if scope_approach_field and scope_approach_field.FieldValue is not None
                else ''
            )
            value_delivered = (
                str(value_delivered_field.FieldValue)
                if value_delivered_field and value_delivered_field.FieldValue is not None
                else ''
            )

            # Fallback mechanism: If any field is empty from repository, try aggregated data
            if not business_issues.strip() or not scope_approach.strip() or not value_delivered.strip():
                try:
                    aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

                    # Use fallback values for empty fields only
                    if not business_issues.strip() and aggregated_data.business_issues:
                        business_issues = str(aggregated_data.business_issues)
                    if not scope_approach.strip() and aggregated_data.scope_approach:
                        scope_approach = str(aggregated_data.scope_approach)
                    if not value_delivered.strip() and aggregated_data.value_delivered:
                        value_delivered = str(aggregated_data.value_delivered)

                except Exception as e:
                    logger.warning(f'Failed to get aggregated data fallback for conversation {conversation_id}: {e}')

            # Check if all of the required fields are still empty after fallback
            all_fields_empty = (
                not business_issues.strip() and not scope_approach.strip() and not value_delivered.strip()
            )

            if all_fields_empty:
                # If all required fields are empty, return empty summary fields
                engagement_summary = ''
                one_line_summary = ''

            else:
                # Regenerate normally if all fields have content
                engagement_summary_task = self._regenerate_field_for_conditional(
                    context=context, field_name=QualFieldName.ENGAGEMENT_SUMMARY, tense=tense
                )
                one_line_summary_task = self._regenerate_field_for_conditional(
                    context=context, field_name=QualFieldName.ONE_LINE_DESCRIPTION, tense=tense
                )

                engagement_summary, one_line_summary = await asyncio.gather(
                    engagement_summary_task, one_line_summary_task
                )

            # Save the regenerated fields
            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=QualFieldName.ENGAGEMENT_SUMMARY,
                field_value=engagement_summary,
                formatted_field_value=engagement_summary,
            )
            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=QualFieldName.ONE_LINE_DESCRIPTION,
                field_value=one_line_summary,
                formatted_field_value=one_line_summary,
            )

            # Prepare new fields
            # Ensure original_qual_fields is not None
            if original_qual_fields is None:
                original_qual_fields = QualFields()

            # Update fields, handling None values gracefully
            updated_fields = original_qual_fields.model_dump(exclude_unset=True)
            updated_fields[QualFieldName.ENGAGEMENT_SUMMARY.value] = {'context': engagement_summary, 'snippet': ''}
            updated_fields[QualFieldName.ONE_LINE_DESCRIPTION.value] = {'context': one_line_summary, 'snippet': ''}

            return QualFields.model_validate(updated_fields)

        except Exception as e:
            logger.error(f'Failed to regenerate summary fields conditionally: {e}')
            # Return original fields if regeneration fails to avoid data loss
            return original_qual_fields

    async def _get_summaries_context_for_regeneration(self, conversation_id: UUID) -> str:
        """
        Get context for summary regeneration (same logic as CommandService).

        Args:
            conversation_id: The conversation ID

        Returns:
            Formatted context string for summary generation
        """
        business_issues_field = await self.field_repository.get_latest_value(
            conversation_id, QualFieldName.BUSINESS_ISSUES
        )
        scope_approach_field = await self.field_repository.get_latest_value(
            conversation_id, QualFieldName.SCOPE_APPROACH
        )
        value_delivered_impact_field = await self.field_repository.get_latest_value(
            conversation_id, QualFieldName.VALUE_DELIVERED
        )

        business_issues = business_issues_field.FieldValue if business_issues_field else ''
        scope_approach = scope_approach_field.FieldValue if scope_approach_field else ''
        value_delivered_impact = value_delivered_impact_field.FieldValue if value_delivered_impact_field else ''

        summary_prompt = (
            f'Business Issues: {business_issues}\n\nScope and Approach: {scope_approach}\n\n'
            f'Value Delivered and Impact: {value_delivered_impact}'
        )
        return summary_prompt

    async def _regenerate_field_for_conditional(self, context: str, field_name: QualFieldName, tense: str) -> str:
        """
        Regenerate a specific field using OpenAI (same logic as CommandService).

        Args:
            context: The context for regeneration
            field_name: The field to regenerate

        Returns:
            The regenerated field content
        """
        if not context.strip():
            return ''

        regenerate_data = self.REGENERATE_MAP[field_name]

        system_prompt_obj: SummaryPrompts = regenerate_data['system_prompt']
        user_prompt_obj: SummaryPrompts = regenerate_data['user_prompt']

        system_prompt = system_prompt_obj.get_system_prompt(tense=tense)
        user_prompt = user_prompt_obj.get_user_prompt(text=context, tense=tense)
        max_tokens = regenerate_data['max_tokens']
        response_format = regenerate_data['response_format']

        messages: list[ChatCompletionMessageParam] = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt},
        ]

        response = await self.openai_repository.generate_chat_completion(
            messages=messages,
            max_tokens=max_tokens,
            temperature=0.0,
            response_format=response_format,
        )

        return str(response)
