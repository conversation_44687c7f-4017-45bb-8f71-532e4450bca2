import asyncio
import logging
import re
from typing import Any, List
from uuid import UUID

from constants.extracted_data import (
    COMPANY_SUFFIXES,
    SOURCE_DATA_PROCESSING_PRIORITY,
    ConversationState,
    DataSourceType,
    FieldStatus,
    RequiredField,
)
from constants.message import SystemReplyType
from core.utils import clean_string_from_trailing_dot
from exceptions.entity import EntityNotFoundError
from repositories import ExtractedDataRepository, QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData
from schemas.quals_clients import ClientSearchItem, ClientSearchResponse

from .base import TokenRequiredFieldHandler


__all__ = ['ClientNameHandler']
logger = logging.getLogger(__name__)


class ClientNameHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?\sand\s" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )

    def __init__(
        self,
        quals_clients_repository: QualsClientsRepository,
        extracted_data_repository: ExtractedDataRepository | None = None,
    ):
        self.quals_clients_repository = quals_clients_repository
        self.extracted_data_repository = extracted_data_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
        called_from: str = '',
        conversation_id: UUID | None = None,
    ) -> FieldHandlerResponse:
        """
        Checks for client name and returns a response.
        This is the main entry point for the handler.
        """
        logger.info(f'ClientNameHandler.check_and_get_response was called from {called_from}')

        if confirmed_data.client_name is not None:
            return self._create_confirmed_response()

        client_names = aggregated_data.client_name
        if not client_names:
            client_names = await self._get_client_names_from_context(conversation_id)
        if not client_names:
            return self._create_missing_info_response()

        processed_client_names = await self._postprocess_client_names(client_names, token)

        if len(processed_client_names) > 1:
            return await self._handle_multiple_client_names(processed_client_names, conversation_id, token)

        if len(processed_client_names) == 1:
            return await self._handle_single_client_name(processed_client_names[0], token, called_from)

        return self._create_missing_info_response()

    async def _handle_multiple_client_names(
        self, client_names: List[str], conversation_id: UUID | None, token: str
    ) -> FieldHandlerResponse:
        """Handles cases where multiple client names are extracted."""
        try:
            found_clients = await self._search_multiple_clients(client_names, token)
            if not found_clients:
                await self._cleanup_extracted_client_names(client_names, conversation_id)
                # No clients found in database - ask user to provide client name
                return self._create_multiple_clients_not_found_response()

            if len(found_clients) == 1:
                # Only one client found, return single confirmation response
                return self._create_single_confirmation_response(found_clients[0].name)

            options = [client.name for client in found_clients]
            return self._create_multiple_options_response(options)
        except Exception as e:
            logger.exception(f'Failed to handle multiple client names: {e}')
            # Fallback to showing original client names
            return self._create_multiple_options_response(client_names, FieldStatus.MULTIPLE)

    async def _search_multiple_clients(self, client_names: List[str], token: str) -> List[ClientSearchItem]:
        """Performs parallel searches for multiple client names."""
        search_requests = [ClientSearchRequest(contains=name, page_size=20, page_idx=0) for name in client_names]
        search_results = await asyncio.gather(
            *[self.quals_clients_repository.search_clients(req, token) for req in search_requests],
            return_exceptions=True,
        )

        found_clients: List[ClientSearchItem] = []
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.warning(f"Failed to search for client '{client_names[i]}': {result}")
            elif isinstance(result, ClientSearchResponse):
                found_clients.extend(result.clients)
        return found_clients

    async def _handle_single_client_name(
        self, client_name: str, token: str, called_from: str = ''
    ) -> FieldHandlerResponse:
        """Handles cases where a single client name is extracted."""
        logger.info(f'Handling single client name "{client_name}" from {called_from}')
        try:
            search_result = await self._search_single_client(client_name, token)
            return self._process_single_client_search_result(client_name, search_result)
        except Exception:
            logger.exception(f'Failed to search for client {client_name}')
            return self._create_single_confirmation_response(client_name)

    async def _search_single_client(self, client_name: str, token: str) -> ClientSearchResponse:
        """Performs a search for a single client name."""
        search_request = ClientSearchRequest(contains=client_name, page_size=20, page_idx=0)
        return await self.quals_clients_repository.search_clients(search_request, token)

    def _process_single_client_search_result(
        self, client_name: str, search_result: ClientSearchResponse
    ) -> FieldHandlerResponse:
        """Processes the search result for a single client and returns an appropriate response."""
        if search_result.clients and len(search_result.clients) == 1:
            found_client = search_result.clients[0]
            return self._create_single_confirmation_response(found_client.name)

        if search_result.clients and len(search_result.clients) > 1:
            # Multiple matches found
            options = [client.name for client in search_result.clients]
            return self._create_multiple_options_response(options, FieldStatus.MULTIPLE)

        # No matches found
        return self._create_client_not_found_response(client_name)

    async def _postprocess_client_names(self, client_names: List[str], token: str) -> List[str]:
        """
        Post-processes extracted client names.
        It splits compound names like "Client A Inc. and Client B LLC" and cleans up names.
        """
        processed_names = []
        for name in client_names:
            if 'and' in name.lower():
                # If a name contains 'and', check if it's a single client in the DB
                search_result = await self._search_single_client(name, token)
                if search_result.clients:
                    processed_names.append(name)
                else:
                    # If not found, it might be a compound name
                    processed_names.extend(self._split_compound_client_name(name))
            else:
                processed_names.append(name.removesuffix('.'))
        return list(dict.fromkeys(processed_names))  # Return unique names

    @classmethod
    def _split_compound_client_name(cls, client_name: str) -> List[str]:
        """
        Splits a compound client name into multiple names.
        For example: "Company A Inc. and Company B LLC" -> ["Company A Inc.", "Company B LLC"]
        The logic is kept from the original implementation to avoid behavior changes.
        """
        split_result = cls.CLIENT_NAME_PATTERN.split(client_name)
        split_result = list(filter(None, split_result))

        current_split = 0
        last_split = len(split_result) - 1
        result = []

        while current_split < last_split:
            company_prefix = split_result[current_split]
            if company_prefix.split(maxsplit=1)[0].lower() in COMPANY_SUFFIXES:
                current_split += 1
                company_prefix = ''.join([company_prefix, split_result[current_split].removesuffix('.')])
                if current_split == last_split:
                    result.append(company_prefix)
                    return result

            current_split += 1
            suffix_part = split_result[current_split].split(maxsplit=1)[0].removesuffix('.')
            result.append(''.join([company_prefix, suffix_part]))
            current_split += 1

        if current_split == last_split:
            result.append(split_result[-1].removesuffix('.'))

        return result

    async def validate_value(
        self, token: str, field_value: Any, called_from: str = '', conversation_id: UUID | None = None
    ) -> FieldHandlerResponse:
        """Validates a provided client name."""
        return await self._handle_single_client_name(field_value, token, called_from)

    # region Response Creation Helpers

    def _create_multiple_clients_not_found_response(self) -> FieldHandlerResponse:
        """Creates a response when multiple clients are not found."""
        reply_type = SystemReplyType.PROVIDE_CLIENT_NAME_MULTIPLE_NOT_FOUND
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=reply_type.message_text,
            system_reply_type=reply_type,
            next_expected_field=None,
            options=[],
        )

    def _create_confirmed_response(self) -> FieldHandlerResponse:
        """Creates a response for an already confirmed client name."""
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.CONFIRMED,
            system_message=None,
            system_reply_type=None,
            next_expected_field=None,
        )

    def _create_missing_info_response(self) -> FieldHandlerResponse:
        """Creates a response when client name information is missing."""
        reply_type = SystemReplyType.NEED_INFO_CLIENT_NAME
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=reply_type.message_text,
            system_reply_type=reply_type,
            next_expected_field=None,
        )

    def _create_multiple_options_response(
        self, options: List[str], status: FieldStatus = FieldStatus.PENDING_CONFIRMATION
    ) -> FieldHandlerResponse:
        """Creates a response with multiple client options for the user to choose from."""
        reply_type = SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=status,
            system_message=reply_type.message_text,
            system_reply_type=reply_type,
            options=options,
            next_expected_field=None,
        )

    def _create_client_name_confirmed_response(self, client_name: str) -> FieldHandlerResponse:
        """Creates a response for a single, confirmed client."""
        reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.SINGLE,
            system_message=reply_type.message_text.format(client_name=client_name),
            system_reply_type=reply_type,
            next_expected_field=RequiredField.LDMF_COUNTRY,
            options=[],
        )

    def _create_client_not_found_response(self, client_name: str) -> FieldHandlerResponse:
        """Creates a response for a client that was not found in the database."""
        reply_type = SystemReplyType.CLIENT_NOT_FOUND
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.SINGLE,
            system_message=reply_type.message_text.format(client_name=client_name),
            system_reply_type=reply_type,
            next_expected_field=None,
            options=[],
        )

    def _create_single_confirmation_response(self, client_name: str) -> FieldHandlerResponse:
        """Creates a response to ask for confirmation for a single client name when search fails."""
        reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
        logger.info('%s system_reply_type detected in %s', reply_type, self.__class__.__name__)
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.SINGLE,
            system_message=reply_type.message_text.format(client_name=client_name),
            system_reply_type=reply_type,
            next_expected_field=None,
            confirm_value=client_name,
            options=[],
        )

    async def _cleanup_extracted_client_names(self, client_names: list[str], conversation_id: UUID | None) -> None:
        """Clean up extracted client names from all data sources when no clients are found."""
        logger.info(f'Starting cleanup for conversation {conversation_id} for client names: {client_names}')
        tasks = [
            self._cleanup_client_names_from_data_source(
                data_source_type=dst,
                conversation_id=conversation_id,
                client_names=client_names,
            )
            for dst in SOURCE_DATA_PROCESSING_PRIORITY
        ]
        await asyncio.gather(*tasks)
        logger.info(f'Completed client names cleanup for conversation {conversation_id}')

    async def _cleanup_client_names_from_data_source(
        self, *, data_source_type: DataSourceType, conversation_id: UUID | None, client_names: list[str]
    ) -> None:
        """Clean up client names for a specific data source type."""

        extracted_data_repository = self.extracted_data_repository
        if not conversation_id or not extracted_data_repository:
            logger.warning(
                'Cannot cleanup extracted client names: missing conversation_id or extracted_data_repository'
            )
            return

        try:
            current_data = await extracted_data_repository.get(conversation_id, data_source_type)
        except EntityNotFoundError as exc:
            if exc.entity_type != 'Conversation' or exc.entity_id != conversation_id:
                raise exc
            logger.info(f'No extracted data found for {data_source_type} for conversation {conversation_id}')
            return

        if not current_data or not current_data.client_name:
            return

        logger.info(f'Found data for {data_source_type}: {current_data.client_name}')

        # Clean both sets of names for comparison
        current_client_names = sorted(clean_string_from_trailing_dot(cl) for cl in current_data.client_name)
        nonexisting_client_names_to_cleanup = sorted(clean_string_from_trailing_dot(cl) for cl in client_names)

        logger.info(f'Cleaned current names: {current_client_names}')
        logger.info(f'Cleaned cleanup names: {nonexisting_client_names_to_cleanup}')

        # Check if names match after cleaning
        if current_client_names == nonexisting_client_names_to_cleanup:
            logger.info(f'Names match, proceeding with cleanup for {data_source_type}')
            updated_data = current_data.model_copy(update={'client_name': []})
            await extracted_data_repository.update(updated_data, allow_empty_clients=True)
            logger.info(
                f'Successfully cleaned up client names from {data_source_type} for conversation {conversation_id}'
            )
        else:
            logger.warning(
                f'Names do not match for {data_source_type}. Current: {current_client_names}, To cleanup: {nonexisting_client_names_to_cleanup}. Skipping cleanup.'
            )

    async def _get_client_names_from_context(
        self,
        conversation_id: UUID | None = None,
    ) -> list[str]:
        client_names = []
        if conversation_id and self.extracted_data_repository is not None:
            conversation = await self.extracted_data_repository.conversation_repository.get(conversation_id)
            if conversation is not None:
                state_context = await self.extracted_data_repository.conversation_repository.get_state_context(
                    conversation_id, ConversationState(str(conversation.State))
                )
                if state_context:
                    client_names = state_context.get('proposed_clients', [])
        return client_names
